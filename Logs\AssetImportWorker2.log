Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker2.log
-srvPort
54768
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 160.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56936
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.012043 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 544 ms
Refreshing native plugins compatible for Editor in 109.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.637 seconds
Domain Reload Profiling:
	ReloadAssembly (1638ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1311ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (171ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (56ms)
			SetupLoadedEditorAssemblies (1018ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (680ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (110ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (158ms)
				ProcessInitializeOnLoadMethodAttributes (67ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.015557 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 104.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.561 seconds
Domain Reload Profiling:
	ReloadAssembly (2562ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (38ms)
		EndReloadAssembly (2127ms)
			LoadAssemblies (190ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (365ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (1470ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (20ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (104ms)
				BeforeProcessingInitializeOnLoad (102ms)
				ProcessInitializeOnLoadAttributes (1144ms)
				ProcessInitializeOnLoadMethodAttributes (88ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3710 Unused Serialized files (Serialized files now loaded: 0)
Unloading 177 unused Assets / (0.7 MB). Loaded Objects now: 4131.
Memory consumption went from 175.1 MB to 174.4 MB.
Total: 7.397000 ms (FindLiveObjects: 0.394000 ms CreateObjectMapping: 0.245900 ms MarkObjects: 6.026800 ms  DeleteObjects: 0.729300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 10057.039887 seconds.
  path: Assets/texture/Grass0139_1_350 (1).jpg
  artifactKey: Guid(e0ff847f4b07eb44bb46fc262ae26efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/Grass0139_1_350 (1).jpg using Guid(e0ff847f4b07eb44bb46fc262ae26efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cbd6c69d75122df0fe7480577f6a130b') in 0.165258 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 2.202833 seconds.
  path: Assets/texture/RockGrassy0051_1_350.jpg
  artifactKey: Guid(6914e8b8ac8a0604ab16769f03587c10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/texture/RockGrassy0051_1_350.jpg using Guid(6914e8b8ac8a0604ab16769f03587c10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c1839253db155cc50dd04e32482afe15') in 0.028501 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013948 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.592 seconds
Domain Reload Profiling:
	ReloadAssembly (2592ms)
		BeginReloadAssembly (288ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2135ms)
			LoadAssemblies (187ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (395ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1433ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (1220ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4149.
Memory consumption went from 171.6 MB to 170.9 MB.
Total: 7.501600 ms (FindLiveObjects: 0.444800 ms CreateObjectMapping: 0.262700 ms MarkObjects: 6.087000 ms  DeleteObjects: 0.706100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015782 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.465 seconds
Domain Reload Profiling:
	ReloadAssembly (2466ms)
		BeginReloadAssembly (255ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2058ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (371ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1387ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1158ms)
				ProcessInitializeOnLoadMethodAttributes (101ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4164.
Memory consumption went from 171.6 MB to 170.9 MB.
Total: 7.969100 ms (FindLiveObjects: 0.473500 ms CreateObjectMapping: 0.250700 ms MarkObjects: 6.476300 ms  DeleteObjects: 0.767300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014189 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.374 seconds
Domain Reload Profiling:
	ReloadAssembly (2375ms)
		BeginReloadAssembly (249ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (1972ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (368ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1309ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1093ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4179.
Memory consumption went from 171.6 MB to 170.9 MB.
Total: 9.546800 ms (FindLiveObjects: 0.660500 ms CreateObjectMapping: 0.252000 ms MarkObjects: 7.675600 ms  DeleteObjects: 0.957500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013918 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.549 seconds
Domain Reload Profiling:
	ReloadAssembly (2550ms)
		BeginReloadAssembly (270ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (68ms)
		EndReloadAssembly (2118ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (421ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (113ms)
			SetupLoadedEditorAssemblies (1361ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1134ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4194.
Memory consumption went from 171.6 MB to 170.9 MB.
Total: 7.289000 ms (FindLiveObjects: 0.452200 ms CreateObjectMapping: 0.247100 ms MarkObjects: 5.824600 ms  DeleteObjects: 0.764100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015409 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.386 seconds
Domain Reload Profiling:
	ReloadAssembly (2388ms)
		BeginReloadAssembly (262ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (80ms)
		EndReloadAssembly (1966ms)
			LoadAssemblies (163ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (361ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (81ms)
			SetupLoadedEditorAssemblies (1331ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1116ms)
				ProcessInitializeOnLoadMethodAttributes (88ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4209.
Memory consumption went from 171.7 MB to 171.0 MB.
Total: 9.080300 ms (FindLiveObjects: 0.448200 ms CreateObjectMapping: 0.267700 ms MarkObjects: 7.671100 ms  DeleteObjects: 0.692300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013752 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.496 seconds
Domain Reload Profiling:
	ReloadAssembly (2497ms)
		BeginReloadAssembly (269ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2059ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (359ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1413ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (34ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1164ms)
				ProcessInitializeOnLoadMethodAttributes (100ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4224.
Memory consumption went from 171.7 MB to 171.0 MB.
Total: 7.915400 ms (FindLiveObjects: 0.475100 ms CreateObjectMapping: 0.271000 ms MarkObjects: 6.345300 ms  DeleteObjects: 0.822800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013581 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.364 seconds
Domain Reload Profiling:
	ReloadAssembly (2366ms)
		BeginReloadAssembly (260ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (1941ms)
			LoadAssemblies (174ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (352ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1321ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1102ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4239.
Memory consumption went from 171.7 MB to 171.0 MB.
Total: 8.169200 ms (FindLiveObjects: 0.653900 ms CreateObjectMapping: 0.296100 ms MarkObjects: 6.306000 ms  DeleteObjects: 0.910500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014297 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.475 seconds
Domain Reload Profiling:
	ReloadAssembly (2476ms)
		BeginReloadAssembly (274ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2042ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (404ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (92ms)
			SetupLoadedEditorAssemblies (1333ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1102ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4254.
Memory consumption went from 171.7 MB to 171.0 MB.
Total: 14.179800 ms (FindLiveObjects: 0.677900 ms CreateObjectMapping: 0.463600 ms MarkObjects: 11.706500 ms  DeleteObjects: 1.329600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014191 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.461 seconds
Domain Reload Profiling:
	ReloadAssembly (2462ms)
		BeginReloadAssembly (262ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2047ms)
			LoadAssemblies (161ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (366ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (110ms)
			SetupLoadedEditorAssemblies (1359ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (32ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1127ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4269.
Memory consumption went from 171.7 MB to 171.0 MB.
Total: 10.348900 ms (FindLiveObjects: 0.729900 ms CreateObjectMapping: 0.462200 ms MarkObjects: 8.315400 ms  DeleteObjects: 0.839900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014100 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.402 seconds
Domain Reload Profiling:
	ReloadAssembly (2403ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (68ms)
		EndReloadAssembly (1976ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (351ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1350ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1119ms)
				ProcessInitializeOnLoadMethodAttributes (102ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4284.
Memory consumption went from 171.8 MB to 171.1 MB.
Total: 7.200500 ms (FindLiveObjects: 0.460200 ms CreateObjectMapping: 0.252300 ms MarkObjects: 5.732800 ms  DeleteObjects: 0.754100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014012 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 5.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.572 seconds
Domain Reload Profiling:
	ReloadAssembly (2573ms)
		BeginReloadAssembly (271ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2118ms)
			LoadAssemblies (181ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (435ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (118ms)
			SetupLoadedEditorAssemblies (1337ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (39ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (6ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1097ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4299.
Memory consumption went from 171.8 MB to 171.1 MB.
Total: 9.689000 ms (FindLiveObjects: 0.637900 ms CreateObjectMapping: 0.256200 ms MarkObjects: 7.774200 ms  DeleteObjects: 1.018500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015432 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.330 seconds
Domain Reload Profiling:
	ReloadAssembly (2332ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (13ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (1927ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (350ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1311ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1095ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4314.
Memory consumption went from 171.8 MB to 171.1 MB.
Total: 14.163500 ms (FindLiveObjects: 0.630300 ms CreateObjectMapping: 0.463500 ms MarkObjects: 11.707600 ms  DeleteObjects: 1.360400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015634 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.423 seconds
Domain Reload Profiling:
	ReloadAssembly (2424ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2014ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (398ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (84ms)
			SetupLoadedEditorAssemblies (1326ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1108ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4329.
Memory consumption went from 171.8 MB to 171.1 MB.
Total: 13.487500 ms (FindLiveObjects: 0.717600 ms CreateObjectMapping: 0.457500 ms MarkObjects: 11.432100 ms  DeleteObjects: 0.878200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019234 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.507 seconds
Domain Reload Profiling:
	ReloadAssembly (2508ms)
		BeginReloadAssembly (234ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (2117ms)
			LoadAssemblies (161ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (352ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (73ms)
			SetupLoadedEditorAssemblies (1482ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1239ms)
				ProcessInitializeOnLoadMethodAttributes (97ms)
				AfterProcessingInitializeOnLoad (19ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4344.
Memory consumption went from 171.8 MB to 171.1 MB.
Total: 7.652900 ms (FindLiveObjects: 0.607100 ms CreateObjectMapping: 0.239000 ms MarkObjects: 6.001500 ms  DeleteObjects: 0.803800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013992 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.443 seconds
Domain Reload Profiling:
	ReloadAssembly (2444ms)
		BeginReloadAssembly (243ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2043ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (368ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1391ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (33ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1145ms)
				ProcessInitializeOnLoadMethodAttributes (102ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4359.
Memory consumption went from 171.8 MB to 171.1 MB.
Total: 8.098400 ms (FindLiveObjects: 0.685500 ms CreateObjectMapping: 0.349100 ms MarkObjects: 6.253500 ms  DeleteObjects: 0.808800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014342 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.363 seconds
Domain Reload Profiling:
	ReloadAssembly (2364ms)
		BeginReloadAssembly (252ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1955ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (342ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1342ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (86ms)
				ProcessInitializeOnLoadAttributes (1119ms)
				ProcessInitializeOnLoadMethodAttributes (100ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4374.
Memory consumption went from 171.9 MB to 171.2 MB.
Total: 7.930400 ms (FindLiveObjects: 0.553800 ms CreateObjectMapping: 0.282500 ms MarkObjects: 6.248100 ms  DeleteObjects: 0.844200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013969 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.498 seconds
Domain Reload Profiling:
	ReloadAssembly (2499ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (2070ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (364ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (86ms)
			SetupLoadedEditorAssemblies (1424ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1191ms)
				ProcessInitializeOnLoadMethodAttributes (97ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4389.
Memory consumption went from 171.9 MB to 171.2 MB.
Total: 7.349400 ms (FindLiveObjects: 0.478500 ms CreateObjectMapping: 0.248800 ms MarkObjects: 5.856100 ms  DeleteObjects: 0.765000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015868 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.363 seconds
Domain Reload Profiling:
	ReloadAssembly (2365ms)
		BeginReloadAssembly (239ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1966ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (345ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1348ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1115ms)
				ProcessInitializeOnLoadMethodAttributes (108ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4404.
Memory consumption went from 171.9 MB to 171.2 MB.
Total: 7.658800 ms (FindLiveObjects: 0.463600 ms CreateObjectMapping: 0.262700 ms MarkObjects: 6.153300 ms  DeleteObjects: 0.778000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015773 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.473 seconds
Domain Reload Profiling:
	ReloadAssembly (2474ms)
		BeginReloadAssembly (264ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (2037ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (368ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1371ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1144ms)
				ProcessInitializeOnLoadMethodAttributes (88ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4419.
Memory consumption went from 171.9 MB to 171.2 MB.
Total: 6.067600 ms (FindLiveObjects: 0.504600 ms CreateObjectMapping: 0.271300 ms MarkObjects: 4.514100 ms  DeleteObjects: 0.776300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014401 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.326 seconds
Domain Reload Profiling:
	ReloadAssembly (2327ms)
		BeginReloadAssembly (257ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (1916ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (345ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1308ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (1096ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4434.
Memory consumption went from 171.9 MB to 171.2 MB.
Total: 9.466700 ms (FindLiveObjects: 0.645100 ms CreateObjectMapping: 0.302000 ms MarkObjects: 7.623500 ms  DeleteObjects: 0.894700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017321 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.568 seconds
Domain Reload Profiling:
	ReloadAssembly (2569ms)
		BeginReloadAssembly (278ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (75ms)
		EndReloadAssembly (2110ms)
			LoadAssemblies (176ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (403ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1392ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1163ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4449.
Memory consumption went from 172.0 MB to 171.2 MB.
Total: 8.302800 ms (FindLiveObjects: 0.572700 ms CreateObjectMapping: 0.254400 ms MarkObjects: 6.728800 ms  DeleteObjects: 0.745900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013945 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.437 seconds
Domain Reload Profiling:
	ReloadAssembly (2438ms)
		BeginReloadAssembly (284ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1982ms)
			LoadAssemblies (182ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (374ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (81ms)
			SetupLoadedEditorAssemblies (1333ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1102ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4464.
Memory consumption went from 172.0 MB to 171.3 MB.
Total: 8.981800 ms (FindLiveObjects: 0.716900 ms CreateObjectMapping: 0.333200 ms MarkObjects: 6.974300 ms  DeleteObjects: 0.955700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013709 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.439 seconds
Domain Reload Profiling:
	ReloadAssembly (2440ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2026ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (368ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1374ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1145ms)
				ProcessInitializeOnLoadMethodAttributes (100ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4479.
Memory consumption went from 172.0 MB to 171.3 MB.
Total: 7.833900 ms (FindLiveObjects: 0.687000 ms CreateObjectMapping: 0.297700 ms MarkObjects: 6.004500 ms  DeleteObjects: 0.843300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015727 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.417 seconds
Domain Reload Profiling:
	ReloadAssembly (2419ms)
		BeginReloadAssembly (264ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (1978ms)
			LoadAssemblies (178ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (384ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (71ms)
			SetupLoadedEditorAssemblies (1338ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1119ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4494.
Memory consumption went from 172.0 MB to 171.3 MB.
Total: 7.884900 ms (FindLiveObjects: 0.517100 ms CreateObjectMapping: 0.252500 ms MarkObjects: 6.279600 ms  DeleteObjects: 0.834000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014196 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.443 seconds
Domain Reload Profiling:
	ReloadAssembly (2444ms)
		BeginReloadAssembly (244ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (2040ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (373ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1389ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (107ms)
				ProcessInitializeOnLoadAttributes (1150ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4509.
Memory consumption went from 172.0 MB to 171.3 MB.
Total: 8.232600 ms (FindLiveObjects: 0.763200 ms CreateObjectMapping: 0.339900 ms MarkObjects: 6.325300 ms  DeleteObjects: 0.803100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015410 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.499 seconds
Domain Reload Profiling:
	ReloadAssembly (2500ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (2062ms)
			LoadAssemblies (172ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (377ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1377ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (1143ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4524.
Memory consumption went from 172.1 MB to 171.4 MB.
Total: 8.573500 ms (FindLiveObjects: 0.484200 ms CreateObjectMapping: 0.254600 ms MarkObjects: 6.978800 ms  DeleteObjects: 0.854800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014076 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.381 seconds
Domain Reload Profiling:
	ReloadAssembly (2382ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (1991ms)
			LoadAssemblies (150ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (362ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1358ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1141ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4539.
Memory consumption went from 172.1 MB to 171.4 MB.
Total: 7.831600 ms (FindLiveObjects: 0.493000 ms CreateObjectMapping: 0.377000 ms MarkObjects: 6.044600 ms  DeleteObjects: 0.915900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014539 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.519 seconds
Domain Reload Profiling:
	ReloadAssembly (2520ms)
		BeginReloadAssembly (262ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (74ms)
		EndReloadAssembly (2107ms)
			LoadAssemblies (178ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (416ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (91ms)
			SetupLoadedEditorAssemblies (1382ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (43ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1141ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4554.
Memory consumption went from 172.1 MB to 171.4 MB.
Total: 7.981500 ms (FindLiveObjects: 0.641800 ms CreateObjectMapping: 0.260500 ms MarkObjects: 6.292200 ms  DeleteObjects: 0.785700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014207 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.395 seconds
Domain Reload Profiling:
	ReloadAssembly (2397ms)
		BeginReloadAssembly (243ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (1998ms)
			LoadAssemblies (147ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (364ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (1351ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (39ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (1101ms)
				ProcessInitializeOnLoadMethodAttributes (102ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4569.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 10.796000 ms (FindLiveObjects: 0.519100 ms CreateObjectMapping: 0.268900 ms MarkObjects: 9.150100 ms  DeleteObjects: 0.856600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014297 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.440 seconds
Domain Reload Profiling:
	ReloadAssembly (2441ms)
		BeginReloadAssembly (259ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2030ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (359ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (81ms)
			SetupLoadedEditorAssemblies (1385ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1156ms)
				ProcessInitializeOnLoadMethodAttributes (99ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4584.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 8.590900 ms (FindLiveObjects: 0.540500 ms CreateObjectMapping: 0.251400 ms MarkObjects: 6.609600 ms  DeleteObjects: 1.187700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014433 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.557 seconds
Domain Reload Profiling:
	ReloadAssembly (2558ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2159ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (377ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (114ms)
			SetupLoadedEditorAssemblies (1459ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (102ms)
				ProcessInitializeOnLoadAttributes (1218ms)
				ProcessInitializeOnLoadMethodAttributes (100ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4599.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 7.964700 ms (FindLiveObjects: 0.548500 ms CreateObjectMapping: 0.250800 ms MarkObjects: 6.340000 ms  DeleteObjects: 0.824100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014501 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.327 seconds
Domain Reload Profiling:
	ReloadAssembly (2328ms)
		BeginReloadAssembly (230ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (1938ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (349ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1324ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1108ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4614.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 9.046000 ms (FindLiveObjects: 0.612100 ms CreateObjectMapping: 0.276000 ms MarkObjects: 7.200800 ms  DeleteObjects: 0.955800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015796 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.507 seconds
Domain Reload Profiling:
	ReloadAssembly (2508ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (2061ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (412ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (90ms)
			SetupLoadedEditorAssemblies (1353ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (115ms)
				ProcessInitializeOnLoadAttributes (1109ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4629.
Memory consumption went from 172.1 MB to 171.4 MB.
Total: 7.822700 ms (FindLiveObjects: 0.520600 ms CreateObjectMapping: 0.258200 ms MarkObjects: 6.190400 ms  DeleteObjects: 0.852400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013834 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.380 seconds
Domain Reload Profiling:
	ReloadAssembly (2381ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (1944ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (347ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1326ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1097ms)
				ProcessInitializeOnLoadMethodAttributes (102ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4644.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 7.589400 ms (FindLiveObjects: 0.446400 ms CreateObjectMapping: 0.253700 ms MarkObjects: 6.044900 ms  DeleteObjects: 0.843100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014273 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.488 seconds
Domain Reload Profiling:
	ReloadAssembly (2489ms)
		BeginReloadAssembly (255ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (75ms)
		EndReloadAssembly (2080ms)
			LoadAssemblies (161ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (366ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1441ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (39ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (106ms)
				ProcessInitializeOnLoadAttributes (1189ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4659.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 6.822500 ms (FindLiveObjects: 0.494400 ms CreateObjectMapping: 0.252200 ms MarkObjects: 5.347100 ms  DeleteObjects: 0.727200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013949 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.478 seconds
Domain Reload Profiling:
	ReloadAssembly (2479ms)
		BeginReloadAssembly (252ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (2068ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (392ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (81ms)
			SetupLoadedEditorAssemblies (1373ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1153ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.31 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4674.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 8.401100 ms (FindLiveObjects: 0.677500 ms CreateObjectMapping: 0.255000 ms MarkObjects: 6.647900 ms  DeleteObjects: 0.819200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014240 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.472 seconds
Domain Reload Profiling:
	ReloadAssembly (2473ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (2067ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (382ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (90ms)
			SetupLoadedEditorAssemblies (1381ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (27ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (106ms)
				ProcessInitializeOnLoadAttributes (1133ms)
				ProcessInitializeOnLoadMethodAttributes (100ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4689.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 7.578400 ms (FindLiveObjects: 0.499800 ms CreateObjectMapping: 0.252800 ms MarkObjects: 6.038600 ms  DeleteObjects: 0.786000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014367 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.452 seconds
Domain Reload Profiling:
	ReloadAssembly (2453ms)
		BeginReloadAssembly (257ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2025ms)
			LoadAssemblies (168ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (364ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1373ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1142ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4704.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 7.777500 ms (FindLiveObjects: 0.525100 ms CreateObjectMapping: 0.253300 ms MarkObjects: 6.138900 ms  DeleteObjects: 0.858900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014236 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.340 seconds
Domain Reload Profiling:
	ReloadAssembly (2341ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (1928ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (342ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (72ms)
			SetupLoadedEditorAssemblies (1328ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1111ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4719.
Memory consumption went from 172.2 MB to 171.5 MB.
Total: 7.282100 ms (FindLiveObjects: 0.503300 ms CreateObjectMapping: 0.248500 ms MarkObjects: 5.769600 ms  DeleteObjects: 0.759700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014096 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.449 seconds
Domain Reload Profiling:
	ReloadAssembly (2450ms)
		BeginReloadAssembly (268ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2022ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (390ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (91ms)
			SetupLoadedEditorAssemblies (1337ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1107ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4734.
Memory consumption went from 172.3 MB to 171.6 MB.
Total: 8.494300 ms (FindLiveObjects: 0.505100 ms CreateObjectMapping: 0.255700 ms MarkObjects: 6.748000 ms  DeleteObjects: 0.984200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014589 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.282 seconds
Domain Reload Profiling:
	ReloadAssembly (2283ms)
		BeginReloadAssembly (244ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (51ms)
		EndReloadAssembly (1866ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (337ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (70ms)
			SetupLoadedEditorAssemblies (1281ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (1068ms)
				ProcessInitializeOnLoadMethodAttributes (94ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4749.
Memory consumption went from 172.3 MB to 171.6 MB.
Total: 8.460500 ms (FindLiveObjects: 0.575300 ms CreateObjectMapping: 0.250100 ms MarkObjects: 6.633900 ms  DeleteObjects: 0.999200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016088 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.553 seconds
Domain Reload Profiling:
	ReloadAssembly (2554ms)
		BeginReloadAssembly (281ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2092ms)
			LoadAssemblies (195ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (400ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1384ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1158ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4764.
Memory consumption went from 172.3 MB to 171.6 MB.
Total: 7.710700 ms (FindLiveObjects: 0.534600 ms CreateObjectMapping: 0.292000 ms MarkObjects: 6.065600 ms  DeleteObjects: 0.817300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015411 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.427 seconds
Domain Reload Profiling:
	ReloadAssembly (2428ms)
		BeginReloadAssembly (259ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2018ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (365ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (1342ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1122ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4779.
Memory consumption went from 172.3 MB to 171.6 MB.
Total: 7.730800 ms (FindLiveObjects: 0.710500 ms CreateObjectMapping: 0.302000 ms MarkObjects: 5.666900 ms  DeleteObjects: 1.049700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014190 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.414 seconds
Domain Reload Profiling:
	ReloadAssembly (2416ms)
		BeginReloadAssembly (273ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (1968ms)
			LoadAssemblies (180ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (358ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1334ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1115ms)
				ProcessInitializeOnLoadMethodAttributes (95ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4794.
Memory consumption went from 172.3 MB to 171.6 MB.
Total: 8.042100 ms (FindLiveObjects: 0.573800 ms CreateObjectMapping: 0.269900 ms MarkObjects: 6.231200 ms  DeleteObjects: 0.965900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015265 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.472 seconds
Domain Reload Profiling:
	ReloadAssembly (2473ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (2047ms)
			LoadAssemblies (176ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (372ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1406ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1181ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4809.
Memory consumption went from 172.4 MB to 171.7 MB.
Total: 7.932100 ms (FindLiveObjects: 0.574700 ms CreateObjectMapping: 0.264400 ms MarkObjects: 6.262700 ms  DeleteObjects: 0.829100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013823 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.427 seconds
Domain Reload Profiling:
	ReloadAssembly (2428ms)
		BeginReloadAssembly (257ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (2015ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (356ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1375ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1146ms)
				ProcessInitializeOnLoadMethodAttributes (100ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4824.
Memory consumption went from 172.4 MB to 171.7 MB.
Total: 8.256100 ms (FindLiveObjects: 0.820300 ms CreateObjectMapping: 0.474600 ms MarkObjects: 6.174500 ms  DeleteObjects: 0.785200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013888 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.477 seconds
Domain Reload Profiling:
	ReloadAssembly (2478ms)
		BeginReloadAssembly (276ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2040ms)
			LoadAssemblies (175ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (385ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1364ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1133ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4839.
Memory consumption went from 172.4 MB to 171.7 MB.
Total: 7.554000 ms (FindLiveObjects: 0.574700 ms CreateObjectMapping: 0.253800 ms MarkObjects: 5.986600 ms  DeleteObjects: 0.737600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014323 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.444 seconds
Domain Reload Profiling:
	ReloadAssembly (2445ms)
		BeginReloadAssembly (265ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2022ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (381ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1349ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1122ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4854.
Memory consumption went from 172.4 MB to 171.7 MB.
Total: 7.611100 ms (FindLiveObjects: 0.599500 ms CreateObjectMapping: 0.256600 ms MarkObjects: 5.972100 ms  DeleteObjects: 0.781700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.021719 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.489 seconds
Domain Reload Profiling:
	ReloadAssembly (2490ms)
		BeginReloadAssembly (276ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (2037ms)
			LoadAssemblies (185ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (365ms)
			ReleaseScriptCaches (4ms)
			RebuildScriptCaches (124ms)
			SetupLoadedEditorAssemblies (1340ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1126ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4869.
Memory consumption went from 172.4 MB to 171.7 MB.
Total: 8.215900 ms (FindLiveObjects: 0.782700 ms CreateObjectMapping: 0.277000 ms MarkObjects: 6.220800 ms  DeleteObjects: 0.933000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013948 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.443 seconds
Domain Reload Profiling:
	ReloadAssembly (2444ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (2000ms)
			LoadAssemblies (175ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (374ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (88ms)
			SetupLoadedEditorAssemblies (1316ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1088ms)
				ProcessInitializeOnLoadMethodAttributes (88ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (18ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4884.
Memory consumption went from 172.4 MB to 171.7 MB.
Total: 8.216700 ms (FindLiveObjects: 0.592500 ms CreateObjectMapping: 0.301000 ms MarkObjects: 6.309700 ms  DeleteObjects: 1.012300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015682 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.482 seconds
Domain Reload Profiling:
	ReloadAssembly (2483ms)
		BeginReloadAssembly (276ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (75ms)
		EndReloadAssembly (2027ms)
			LoadAssemblies (183ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (383ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1334ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1117ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4899.
Memory consumption went from 172.5 MB to 171.8 MB.
Total: 8.253500 ms (FindLiveObjects: 0.660700 ms CreateObjectMapping: 0.323200 ms MarkObjects: 6.347900 ms  DeleteObjects: 0.920400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014032 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.453 seconds
Domain Reload Profiling:
	ReloadAssembly (2455ms)
		BeginReloadAssembly (270ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (2008ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (350ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (109ms)
			SetupLoadedEditorAssemblies (1344ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (86ms)
				ProcessInitializeOnLoadAttributes (1118ms)
				ProcessInitializeOnLoadMethodAttributes (102ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4914.
Memory consumption went from 172.5 MB to 171.8 MB.
Total: 7.666200 ms (FindLiveObjects: 0.533500 ms CreateObjectMapping: 0.265000 ms MarkObjects: 6.091000 ms  DeleteObjects: 0.775800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015404 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.495 seconds
Domain Reload Profiling:
	ReloadAssembly (2496ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2089ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (397ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1402ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (100ms)
				ProcessInitializeOnLoadAttributes (1165ms)
				ProcessInitializeOnLoadMethodAttributes (101ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 5.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4929.
Memory consumption went from 172.5 MB to 171.8 MB.
Total: 7.903700 ms (FindLiveObjects: 0.607800 ms CreateObjectMapping: 0.261300 ms MarkObjects: 6.172700 ms  DeleteObjects: 0.860300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013752 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.492 seconds
Domain Reload Profiling:
	ReloadAssembly (2493ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2072ms)
			LoadAssemblies (175ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (410ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (1365ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1139ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4944.
Memory consumption went from 172.5 MB to 171.8 MB.
Total: 9.430400 ms (FindLiveObjects: 0.738100 ms CreateObjectMapping: 0.264800 ms MarkObjects: 7.330100 ms  DeleteObjects: 1.096300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015012 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.496 seconds
Domain Reload Profiling:
	ReloadAssembly (2497ms)
		BeginReloadAssembly (256ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2060ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (363ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (97ms)
			SetupLoadedEditorAssemblies (1402ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (102ms)
				ProcessInitializeOnLoadAttributes (1167ms)
				ProcessInitializeOnLoadMethodAttributes (94ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4959.
Memory consumption went from 172.5 MB to 171.8 MB.
Total: 8.205400 ms (FindLiveObjects: 0.693500 ms CreateObjectMapping: 0.269100 ms MarkObjects: 6.344200 ms  DeleteObjects: 0.897400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013687 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.372 seconds
Domain Reload Profiling:
	ReloadAssembly (2373ms)
		BeginReloadAssembly (290ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (88ms)
		EndReloadAssembly (1928ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (350ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1315ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (86ms)
				ProcessInitializeOnLoadAttributes (1103ms)
				ProcessInitializeOnLoadMethodAttributes (89ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4974.
Memory consumption went from 172.5 MB to 171.8 MB.
Total: 8.011000 ms (FindLiveObjects: 0.818500 ms CreateObjectMapping: 0.287100 ms MarkObjects: 6.060300 ms  DeleteObjects: 0.843100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014366 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.476 seconds
Domain Reload Profiling:
	ReloadAssembly (2477ms)
		BeginReloadAssembly (264ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (74ms)
		EndReloadAssembly (2051ms)
			LoadAssemblies (148ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (387ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1363ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1133ms)
				ProcessInitializeOnLoadMethodAttributes (97ms)
				AfterProcessingInitializeOnLoad (17ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.64 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 4989.
Memory consumption went from 172.6 MB to 171.9 MB.
Total: 7.559300 ms (FindLiveObjects: 0.612600 ms CreateObjectMapping: 0.257700 ms MarkObjects: 5.899300 ms  DeleteObjects: 0.788200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015923 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.385 seconds
Domain Reload Profiling:
	ReloadAssembly (2386ms)
		BeginReloadAssembly (250ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (1964ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (348ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1332ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1115ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5004.
Memory consumption went from 172.6 MB to 171.9 MB.
Total: 9.141100 ms (FindLiveObjects: 1.029500 ms CreateObjectMapping: 0.305400 ms MarkObjects: 6.850200 ms  DeleteObjects: 0.954500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015769 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.443 seconds
Domain Reload Profiling:
	ReloadAssembly (2444ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (51ms)
		EndReloadAssembly (2037ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (361ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (117ms)
			SetupLoadedEditorAssemblies (1351ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1128ms)
				ProcessInitializeOnLoadMethodAttributes (95ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5019.
Memory consumption went from 172.6 MB to 171.9 MB.
Total: 7.964800 ms (FindLiveObjects: 0.628000 ms CreateObjectMapping: 0.250100 ms MarkObjects: 6.214500 ms  DeleteObjects: 0.871100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014121 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.341 seconds
Domain Reload Profiling:
	ReloadAssembly (2342ms)
		BeginReloadAssembly (235ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (60ms)
		EndReloadAssembly (1953ms)
			LoadAssemblies (152ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (346ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (72ms)
			SetupLoadedEditorAssemblies (1350ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1134ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5034.
Memory consumption went from 172.6 MB to 171.9 MB.
Total: 7.324000 ms (FindLiveObjects: 0.521300 ms CreateObjectMapping: 0.253400 ms MarkObjects: 5.797600 ms  DeleteObjects: 0.750900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013901 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.572 seconds
Domain Reload Profiling:
	ReloadAssembly (2573ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (2147ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (386ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1446ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (112ms)
				ProcessInitializeOnLoadAttributes (1198ms)
				ProcessInitializeOnLoadMethodAttributes (96ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5049.
Memory consumption went from 172.6 MB to 171.9 MB.
Total: 7.976500 ms (FindLiveObjects: 0.713300 ms CreateObjectMapping: 0.347400 ms MarkObjects: 6.138500 ms  DeleteObjects: 0.776200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013833 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.460 seconds
Domain Reload Profiling:
	ReloadAssembly (2461ms)
		BeginReloadAssembly (251ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (2056ms)
			LoadAssemblies (153ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (396ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (89ms)
			SetupLoadedEditorAssemblies (1374ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1147ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5064.
Memory consumption went from 172.6 MB to 171.9 MB.
Total: 6.859800 ms (FindLiveObjects: 0.555800 ms CreateObjectMapping: 0.260200 ms MarkObjects: 5.268400 ms  DeleteObjects: 0.774200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015720 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.559 seconds
Domain Reload Profiling:
	ReloadAssembly (2560ms)
		BeginReloadAssembly (265ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2119ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (386ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (1393ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1161ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5079.
Memory consumption went from 172.7 MB to 172.0 MB.
Total: 8.078100 ms (FindLiveObjects: 0.601400 ms CreateObjectMapping: 0.256200 ms MarkObjects: 6.326900 ms  DeleteObjects: 0.892400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015239 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.541 seconds
Domain Reload Profiling:
	ReloadAssembly (2542ms)
		BeginReloadAssembly (251ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (2137ms)
			LoadAssemblies (156ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (375ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1424ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1190ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (12ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5094.
Memory consumption went from 172.7 MB to 172.0 MB.
Total: 7.179900 ms (FindLiveObjects: 0.614900 ms CreateObjectMapping: 0.253500 ms MarkObjects: 5.551000 ms  DeleteObjects: 0.759000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014265 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  4.159 seconds
Domain Reload Profiling:
	ReloadAssembly (4160ms)
		BeginReloadAssembly (264ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (3743ms)
			LoadAssemblies (168ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (430ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (948ms)
			SetupLoadedEditorAssemblies (1548ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (29ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (199ms)
				ProcessInitializeOnLoadAttributes (1191ms)
				ProcessInitializeOnLoadMethodAttributes (112ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5109.
Memory consumption went from 172.7 MB to 172.0 MB.
Total: 8.227800 ms (FindLiveObjects: 0.688000 ms CreateObjectMapping: 0.273300 ms MarkObjects: 6.442000 ms  DeleteObjects: 0.823400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014375 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.452 seconds
Domain Reload Profiling:
	ReloadAssembly (2453ms)
		BeginReloadAssembly (287ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1994ms)
			LoadAssemblies (198ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (353ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1352ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (33ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (1118ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5124.
Memory consumption went from 172.7 MB to 172.0 MB.
Total: 7.866000 ms (FindLiveObjects: 0.608300 ms CreateObjectMapping: 0.261800 ms MarkObjects: 6.173500 ms  DeleteObjects: 0.821100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014251 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.347 seconds
Domain Reload Profiling:
	ReloadAssembly (2348ms)
		BeginReloadAssembly (238ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1956ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (347ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1324ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1106ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (18ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5139.
Memory consumption went from 173.0 MB to 172.3 MB.
Total: 11.358800 ms (FindLiveObjects: 0.888200 ms CreateObjectMapping: 0.480200 ms MarkObjects: 9.225800 ms  DeleteObjects: 0.763000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014129 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.459 seconds
Domain Reload Profiling:
	ReloadAssembly (2460ms)
		BeginReloadAssembly (273ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (80ms)
		EndReloadAssembly (2027ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (376ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (91ms)
			SetupLoadedEditorAssemblies (1348ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1130ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5154.
Memory consumption went from 172.8 MB to 172.1 MB.
Total: 7.574200 ms (FindLiveObjects: 0.645400 ms CreateObjectMapping: 0.281000 ms MarkObjects: 5.814300 ms  DeleteObjects: 0.832300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013844 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.415 seconds
Domain Reload Profiling:
	ReloadAssembly (2416ms)
		BeginReloadAssembly (251ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (53ms)
		EndReloadAssembly (2008ms)
			LoadAssemblies (168ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (358ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1377ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1151ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5169.
Memory consumption went from 172.8 MB to 172.1 MB.
Total: 7.889000 ms (FindLiveObjects: 0.572500 ms CreateObjectMapping: 0.260300 ms MarkObjects: 6.216300 ms  DeleteObjects: 0.838500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015593 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.555 seconds
Domain Reload Profiling:
	ReloadAssembly (2556ms)
		BeginReloadAssembly (263ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (2133ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (406ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1423ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1194ms)
				ProcessInitializeOnLoadMethodAttributes (87ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.78 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5184.
Memory consumption went from 172.9 MB to 172.2 MB.
Total: 8.635800 ms (FindLiveObjects: 0.847700 ms CreateObjectMapping: 0.303300 ms MarkObjects: 6.551400 ms  DeleteObjects: 0.932200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015309 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.712 seconds
Domain Reload Profiling:
	ReloadAssembly (2713ms)
		BeginReloadAssembly (277ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (14ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2238ms)
			LoadAssemblies (183ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (427ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1489ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1238ms)
				ProcessInitializeOnLoadMethodAttributes (108ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5199.
Memory consumption went from 172.9 MB to 172.2 MB.
Total: 8.072700 ms (FindLiveObjects: 0.644700 ms CreateObjectMapping: 0.269000 ms MarkObjects: 6.357200 ms  DeleteObjects: 0.800900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014064 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.376 seconds
Domain Reload Profiling:
	ReloadAssembly (2377ms)
		BeginReloadAssembly (257ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (1967ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (343ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1362ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1146ms)
				ProcessInitializeOnLoadMethodAttributes (88ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5214.
Memory consumption went from 172.9 MB to 172.2 MB.
Total: 7.804200 ms (FindLiveObjects: 0.582500 ms CreateObjectMapping: 0.261000 ms MarkObjects: 6.140300 ms  DeleteObjects: 0.819100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014287 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.512 seconds
Domain Reload Profiling:
	ReloadAssembly (2513ms)
		BeginReloadAssembly (233ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (53ms)
		EndReloadAssembly (2119ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (357ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (1458ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1230ms)
				ProcessInitializeOnLoadMethodAttributes (90ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5229.
Memory consumption went from 172.9 MB to 172.2 MB.
Total: 8.040300 ms (FindLiveObjects: 0.740200 ms CreateObjectMapping: 0.265800 ms MarkObjects: 6.187200 ms  DeleteObjects: 0.834700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015699 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.438 seconds
Domain Reload Profiling:
	ReloadAssembly (2439ms)
		BeginReloadAssembly (249ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (2034ms)
			LoadAssemblies (168ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (350ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1365ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (1141ms)
				ProcessInitializeOnLoadMethodAttributes (88ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5244.
Memory consumption went from 173.1 MB to 172.5 MB.
Total: 7.776600 ms (FindLiveObjects: 0.539200 ms CreateObjectMapping: 0.255000 ms MarkObjects: 6.237200 ms  DeleteObjects: 0.744000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013872 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.430 seconds
Domain Reload Profiling:
	ReloadAssembly (2431ms)
		BeginReloadAssembly (257ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (68ms)
		EndReloadAssembly (2012ms)
			LoadAssemblies (163ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (357ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1346ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1113ms)
				ProcessInitializeOnLoadMethodAttributes (102ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5259.
Memory consumption went from 173.0 MB to 172.3 MB.
Total: 8.117200 ms (FindLiveObjects: 0.741800 ms CreateObjectMapping: 0.260000 ms MarkObjects: 6.309500 ms  DeleteObjects: 0.804800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013842 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.457 seconds
Domain Reload Profiling:
	ReloadAssembly (2458ms)
		BeginReloadAssembly (231ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (2067ms)
			LoadAssemblies (152ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (372ms)
			ReleaseScriptCaches (4ms)
			RebuildScriptCaches (96ms)
			SetupLoadedEditorAssemblies (1389ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (110ms)
				ProcessInitializeOnLoadAttributes (1148ms)
				ProcessInitializeOnLoadMethodAttributes (91ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5274.
Memory consumption went from 173.0 MB to 172.3 MB.
Total: 8.366600 ms (FindLiveObjects: 0.721500 ms CreateObjectMapping: 0.355200 ms MarkObjects: 6.266900 ms  DeleteObjects: 1.021700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013646 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.472 seconds
Domain Reload Profiling:
	ReloadAssembly (2473ms)
		BeginReloadAssembly (270ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (2034ms)
			LoadAssemblies (183ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (368ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1366ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1148ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5289.
Memory consumption went from 173.2 MB to 172.5 MB.
Total: 8.696600 ms (FindLiveObjects: 0.742000 ms CreateObjectMapping: 0.320000 ms MarkObjects: 6.730000 ms  DeleteObjects: 0.903600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015871 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.570 seconds
Domain Reload Profiling:
	ReloadAssembly (2571ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (2121ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (379ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (88ms)
			SetupLoadedEditorAssemblies (1440ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (98ms)
				ProcessInitializeOnLoadAttributes (1210ms)
				ProcessInitializeOnLoadMethodAttributes (92ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5304.
Memory consumption went from 173.2 MB to 172.5 MB.
Total: 8.607800 ms (FindLiveObjects: 1.250700 ms CreateObjectMapping: 0.520900 ms MarkObjects: 6.019600 ms  DeleteObjects: 0.815200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013733 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.454 seconds
Domain Reload Profiling:
	ReloadAssembly (2456ms)
		BeginReloadAssembly (271ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (76ms)
		EndReloadAssembly (2029ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (369ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (90ms)
			SetupLoadedEditorAssemblies (1366ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1134ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5319.
Memory consumption went from 173.2 MB to 172.5 MB.
Total: 9.418600 ms (FindLiveObjects: 0.835100 ms CreateObjectMapping: 0.602300 ms MarkObjects: 7.147100 ms  DeleteObjects: 0.833000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014170 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.457 seconds
Domain Reload Profiling:
	ReloadAssembly (2458ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (60ms)
		EndReloadAssembly (2050ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (391ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1381ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1153ms)
				ProcessInitializeOnLoadMethodAttributes (99ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 4.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5334.
Memory consumption went from 173.3 MB to 172.6 MB.
Total: 7.872400 ms (FindLiveObjects: 0.611800 ms CreateObjectMapping: 0.292200 ms MarkObjects: 6.063800 ms  DeleteObjects: 0.903600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014660 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.598 seconds
Domain Reload Profiling:
	ReloadAssembly (2599ms)
		BeginReloadAssembly (284ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (2133ms)
			LoadAssemblies (174ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (366ms)
			ReleaseScriptCaches (4ms)
			RebuildScriptCaches (92ms)
			SetupLoadedEditorAssemblies (1463ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (89ms)
				ProcessInitializeOnLoadAttributes (1225ms)
				ProcessInitializeOnLoadMethodAttributes (110ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (12ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5349.
Memory consumption went from 173.3 MB to 172.6 MB.
Total: 8.621000 ms (FindLiveObjects: 0.773600 ms CreateObjectMapping: 0.302100 ms MarkObjects: 6.533100 ms  DeleteObjects: 1.011100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013730 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.617 seconds
Domain Reload Profiling:
	ReloadAssembly (2618ms)
		BeginReloadAssembly (270ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (16ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (72ms)
		EndReloadAssembly (2150ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (393ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (100ms)
			SetupLoadedEditorAssemblies (1427ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (88ms)
				ProcessInitializeOnLoadAttributes (1187ms)
				ProcessInitializeOnLoadMethodAttributes (111ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (19ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5364.
Memory consumption went from 173.3 MB to 172.6 MB.
Total: 7.851100 ms (FindLiveObjects: 0.620200 ms CreateObjectMapping: 0.288500 ms MarkObjects: 5.857600 ms  DeleteObjects: 1.083700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014028 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.470 seconds
Domain Reload Profiling:
	ReloadAssembly (2471ms)
		BeginReloadAssembly (255ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (2060ms)
			LoadAssemblies (161ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (361ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (1429ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (101ms)
				ProcessInitializeOnLoadAttributes (1193ms)
				ProcessInitializeOnLoadMethodAttributes (98ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5379.
Memory consumption went from 173.3 MB to 172.6 MB.
Total: 8.236600 ms (FindLiveObjects: 0.885100 ms CreateObjectMapping: 0.298100 ms MarkObjects: 6.224400 ms  DeleteObjects: 0.827400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014146 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.80 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.429 seconds
Domain Reload Profiling:
	ReloadAssembly (2430ms)
		BeginReloadAssembly (266ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (81ms)
		EndReloadAssembly (2008ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (376ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (89ms)
			SetupLoadedEditorAssemblies (1344ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1124ms)
				ProcessInitializeOnLoadMethodAttributes (93ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5394.
Memory consumption went from 173.3 MB to 172.6 MB.
Total: 9.011400 ms (FindLiveObjects: 0.778400 ms CreateObjectMapping: 0.391900 ms MarkObjects: 6.774500 ms  DeleteObjects: 1.065400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014683 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.523 seconds
Domain Reload Profiling:
	ReloadAssembly (2524ms)
		BeginReloadAssembly (243ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (2128ms)
			LoadAssemblies (153ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (394ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (73ms)
			SetupLoadedEditorAssemblies (1469ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (92ms)
				ProcessInitializeOnLoadAttributes (1240ms)
				ProcessInitializeOnLoadMethodAttributes (99ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5409.
Memory consumption went from 173.4 MB to 172.6 MB.
Total: 7.485000 ms (FindLiveObjects: 0.651800 ms CreateObjectMapping: 0.293800 ms MarkObjects: 5.738700 ms  DeleteObjects: 0.799600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013750 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  3.910 seconds
Domain Reload Profiling:
	ReloadAssembly (3911ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (3518ms)
			LoadAssemblies (151ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (379ms)
			ReleaseScriptCaches (4ms)
			RebuildScriptCaches (102ms)
			SetupLoadedEditorAssemblies (2803ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (2565ms)
				ProcessInitializeOnLoadMethodAttributes (111ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5424.
Memory consumption went from 173.4 MB to 172.7 MB.
Total: 9.671100 ms (FindLiveObjects: 0.849400 ms CreateObjectMapping: 0.317600 ms MarkObjects: 7.582800 ms  DeleteObjects: 0.920200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013757 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.478 seconds
Domain Reload Profiling:
	ReloadAssembly (2480ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (2073ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (368ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1431ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (112ms)
				ProcessInitializeOnLoadAttributes (1177ms)
				ProcessInitializeOnLoadMethodAttributes (104ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3668 Unused Serialized files (Serialized files now loaded: 0)
Unloading 158 unused Assets / (0.7 MB). Loaded Objects now: 5439.
Memory consumption went from 173.4 MB to 172.7 MB.
Total: 8.198500 ms (FindLiveObjects: 0.753600 ms CreateObjectMapping: 0.375800 ms MarkObjects: 6.043800 ms  DeleteObjects: 1.023500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 17619.262874 seconds.
  path: Assets/Scon mod Script/plough soil.png
  artifactKey: Guid(b0b9a99a42f193e4db431c843a45d058) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scon mod Script/plough soil.png using Guid(b0b9a99a42f193e4db431c843a45d058) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a2c9e4992906b42c56d24b208d944e10') in 0.118401 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 25.680318 seconds.
  path: Assets/City/Texture2D/New Terrain 2 Splatmap 0.png
  artifactKey: Guid(eecc65d1a984a1642b192a3c5c8e2eff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/City/Texture2D/New Terrain 2 Splatmap 0.png using Guid(eecc65d1a984a1642b192a3c5c8e2eff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '88d62fd7d02ff8a033c173a99a634132') in 0.024789 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.026574 seconds.
  path: Assets/Plough.fbm/Plower_diffuse.png
  artifactKey: Guid(4b7e359b54432ef43b73dc5f99448b87) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plough.fbm/Plower_diffuse.png using Guid(4b7e359b54432ef43b73dc5f99448b87) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4775946daabd1ea28818ef823e62f601') in 0.046059 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Textures/Splat.png
  artifactKey: Guid(00282aad0def9f942a895c41d7389b8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Textures/Splat.png using Guid(00282aad0def9f942a895c41d7389b8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e654a285c6e42fb57bf7431547c9321d') in 0.026911 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/purpleSquare.png
  artifactKey: Guid(dc0ca9b32ae9fd049911671cf1a480bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/purpleSquare.png using Guid(dc0ca9b32ae9fd049911671cf1a480bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4053a347479c694a0a382594a44fc721') in 0.029114 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/GameMy/TubeWail/tube wail (2).fbm/TexturesCom_PlasterColoured0105_1_seamless_S.jpg
  artifactKey: Guid(cae52e840034d464d8c325bfb7db161a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GameMy/TubeWail/tube wail (2).fbm/TexturesCom_PlasterColoured0105_1_seamless_S.jpg using Guid(cae52e840034d464d8c325bfb7db161a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '14091b56fc4b0948fcb8984db26918d3') in 0.122877 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/GameMy/Water_Fountain/WaterEffects/Textures/WaterRipplesParticle.tif
  artifactKey: Guid(8b12f103a80e77a4286dc286688b0228) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/GameMy/Water_Fountain/WaterEffects/Textures/WaterRipplesParticle.tif using Guid(8b12f103a80e77a4286dc286688b0228) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '08950152e69cd7f01610028e15fed30a') in 0.039097 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/new bike/bike.fbm/purple.png
  artifactKey: Guid(cd3a0a2301d7d984081348f80bc95c77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/new bike/bike.fbm/purple.png using Guid(cd3a0a2301d7d984081348f80bc95c77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1bd2c11333a7beac29ad1443790ef1ad') in 0.049992 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/terrinmaterial/New Terrain.asset
  artifactKey: Guid(2c41750172648de42b53f55b9f2f8b47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/terrinmaterial/New Terrain.asset using Guid(2c41750172648de42b53f55b9f2f8b47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '72bdf45503e332ef506c60f15ec18d97') in 0.374375 seconds 
Number of asset objects unloaded after import = 5
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Texture2D/symbol_hexagon_simple.png
  artifactKey: Guid(c5e7233258b60834aaa97e45bc4f57dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Texture2D/symbol_hexagon_simple.png using Guid(c5e7233258b60834aaa97e45bc4f57dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ded47a007132afcb4694b2baf3a72432') in 0.195752 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Game scene/New enviroment texture/Plough.fbm/Plower_diffuse.png
  artifactKey: Guid(f07a7c6065571294b839aece5a280801) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Game scene/New enviroment texture/Plough.fbm/Plower_diffuse.png using Guid(f07a7c6065571294b839aece5a280801) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '13a74ea4b55e1b05db4d45ea9062d9db') in 0.021394 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/BuildReport/GUI/BRTSkin/SimpleBoxRounded.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/BuildReport/GUI/BRTSkin/SimpleBoxRounded.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '155d6a8c2235a484baed8f00e1acefb1') in 0.016757 seconds 
Number of asset objects unloaded after import = 2
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0