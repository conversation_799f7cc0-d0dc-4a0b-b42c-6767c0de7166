using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
    public float paintRadius = 2f;
    public int targetLayerIndex = 0;     // Texture layer to check before painting (e.g. grass)
    public int applyLayerIndex = 1;      // Texture layer to apply (e.g. soil)
    public float minSpeed = 0.1f;        // Minimum speed to start painting
    public float maxSpeed = 10f;         // Maximum speed for painting calculations
    public float paintIntensity = 1f;    // How strong the painting effect is
    public bool smoothTrail = true;      // Enable smooth trail painting
    public float curveSensitivity = 2f;  // Higher values = smoother curves during rotation
    public bool useSquareBrush = true;   // Use square brush instead of circular
    public Vector3 paintOffset = Vector3.zero; // Manual offset for paint position
    public bool showDebugGizmos = false; // Show debug gizmos for paint position

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;
    private Rigidbody rb;
    private bool isGrounded = false;
    private Vector3 lastPosition;
    private float currentSpeed;
    private Vector3 lastPaintPosition;
    private float lastUpdateTime;
    private Vector3 velocity;
    

    void Start()
    {
        terrain = Terrain.activeTerrain;
        rb = GetComponent<Rigidbody>();

        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0,
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }

        lastPosition = transform.position;
        lastPaintPosition = transform.position;
    }

    void Update()
    {
        // Calculate current speed and velocity for better curve detection
        velocity = (transform.position - lastPosition) / Time.deltaTime;
        currentSpeed = velocity.magnitude;
        lastPosition = transform.position;
        lastUpdateTime = Time.time;

        // Check for terrain contact and paint smoothly with improved frequency for curves
        if (smoothTrail && currentSpeed >= minSpeed)
        {
            CheckTerrainContactSmooth();
        }

        // Reset terrain with R key
        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
        }
    }

    void CheckTerrainContactSmooth()
    {
        if (terrain == null) return;

        // Get object's center position (accounting for bounds)
        Vector3 objectCenter = transform.position;

        // If object has a renderer, use its center
        Renderer objectRenderer = GetComponent<Renderer>();
        if (objectRenderer != null)
        {
            objectCenter = objectRenderer.bounds.center;
        }

        // Raycast downward from object center to check if we're on terrain
        Vector3 rayStart = objectCenter + Vector3.up * 0.5f;

        if (Physics.Raycast(rayStart, Vector3.down, out RaycastHit hit, 2f))
        {
            // Check if we hit the terrain
            if (hit.collider.GetComponent<TerrainCollider>())
            {
                isGrounded = true;

                // Use object's center X,Z position but terrain's Y position for accurate painting
                Vector3 exactPaintPos = new Vector3(objectCenter.x + paintOffset.x, hit.point.y, objectCenter.z + paintOffset.z);

                // Paint smooth trail between last position and current exact position
                PaintSmoothTrail(lastPaintPosition, exactPaintPos);
                lastPaintPosition = exactPaintPos;
            }
            else
            {
                isGrounded = false;
            }
        }
        else
        {
            isGrounded = false;
        }
    }

    void PaintSmoothTrail(Vector3 startPos, Vector3 endPos)
    {
        float distance = Vector3.Distance(startPos, endPos);
        // Dynamic steps based on curve sensitivity and speed for smoother rotation trails
        float stepMultiplier = curveSensitivity * (1f + currentSpeed / maxSpeed);
        int steps = Mathf.Max(3, Mathf.RoundToInt(distance / (paintRadius * 0.2f) * stepMultiplier));

        for (int i = 0; i <= steps; i++)
        {
            float t = (float)i / steps;
            Vector3 paintPos = Vector3.Lerp(startPos, endPos, t);

            // Check if this position should be painted
            if (IsTargetLayer(paintPos))
            {
                // Calculate paint strength based on speed and curve smoothness
                float speedFactor = Mathf.Clamp(currentSpeed / minSpeed, 0.3f, 2f);
                float curveFactor = 1f - (t * (1f - t) * 0.5f); // Slightly stronger at ends for better connection
                PaintTextureAtWithIntensity(paintPos, paintIntensity * speedFactor * curveFactor);
            }
        }
    }

    // Alternative collision method (keep both for better compatibility)
    void OnCollisionStay(Collision collision)
    {
        if (terrain == null) return;

        // Check if colliding with Terrain
        if (collision.collider.GetComponent<TerrainCollider>())
        {
            // Loop through all contact points with terrain
            foreach (ContactPoint contact in collision.contacts)
            {
                if (IsTargetLayer(contact.point))
                {
                    PaintTextureAt(contact.point);
                }
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];

        return targetWeight >= 0.5f;
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        PaintTextureAtWithIntensity(worldPos, 1f);
    }

    void PaintTextureAtWithIntensity(Vector3 worldPos, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        // Convert world position to alphamap coordinates
        float mapX = terrainPos.x / terrainData.size.x * terrainData.alphamapWidth;
        float mapZ = terrainPos.z / terrainData.size.z * terrainData.alphamapHeight;

        // Calculate brush size in alphamap units for square pattern
        float brushSizeX = paintRadius / terrainData.size.x * terrainData.alphamapWidth;
        float brushSizeZ = paintRadius / terrainData.size.z * terrainData.alphamapHeight;

        int halfBrushX = Mathf.RoundToInt(brushSizeX * 0.5f);
        int halfBrushZ = Mathf.RoundToInt(brushSizeZ * 0.5f);

        // Calculate square bounds with perfect centering
        int centerX = Mathf.RoundToInt(mapX);
        int centerZ = Mathf.RoundToInt(mapZ);

        int startX = Mathf.Clamp(centerX - halfBrushX, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(centerZ - halfBrushZ, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(centerX + halfBrushX, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(centerZ + halfBrushZ, 0, terrainData.alphamapHeight - 1);

        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);

        // Paint with selected brush pattern
        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float currentWeight = alphaMaps[x, z, targetLayerIndex];
                if (currentWeight >= 0.5f)
                {
                    float falloff = 1f;

                    if (useSquareBrush)
                    {
                        // Square brush with smooth falloff from center
                        float centerDistX = Mathf.Abs(x - (actualWidth * 0.5f)) / (actualWidth * 0.5f);
                        float centerDistZ = Mathf.Abs(z - (actualHeight * 0.5f)) / (actualHeight * 0.5f);
                        float maxDist = Mathf.Max(centerDistX, centerDistZ);
                        falloff = 1f - (maxDist * 0.2f); // 20% falloff at edges for square
                    }
                    else
                    {
                        // Circular brush with distance-based falloff
                        float centerDistX = x - (actualWidth * 0.5f);
                        float centerDistZ = z - (actualHeight * 0.5f);
                        float distance = Mathf.Sqrt(centerDistX * centerDistX + centerDistZ * centerDistZ);
                        float maxRadius = Mathf.Min(actualWidth, actualHeight) * 0.5f;

                        if (distance <= maxRadius)
                        {
                            falloff = 1f - (distance / maxRadius);
                        }
                        else
                        {
                            continue; // Skip pixels outside circle
                        }
                    }

                    falloff = Mathf.Clamp01(falloff * intensity);

                    // Apply texture blending
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 1f, falloff);
                        }
                        else
                        {
                            alphaMaps[x, z, i] = Mathf.Lerp(alphaMaps[x, z, i], 0f, falloff);
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }


}
