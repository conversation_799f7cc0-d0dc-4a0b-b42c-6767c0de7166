using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
    public float paintRadius = 2f;
    public int targetLayerIndex = 0;     // Texture layer to check before painting (e.g. grass)
    public int applyLayerIndex = 1;      // Texture layer to apply (e.g. soil)
    public float minSpeed = 0.1f;        // Minimum speed to start painting
    public float maxSpeed = 10f;         // Maximum speed for painting calculations
    public float paintIntensity = 1f;    // How strong the painting effect is
    public bool smoothTrail = true;      // Enable smooth trail painting

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;
    private Rigidbody rb;
    private bool isGrounded = false;
    private Vector3 lastPosition;
    private float currentSpeed;
    private Vector3 lastPaintPosition;
    public par

    void Start()
    {
        terrain = Terrain.activeTerrain;
        rb = GetComponent<Rigidbody>();

        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0,
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }

        lastPosition = transform.position;
        lastPaintPosition = transform.position;
    }

    void Update()
    {
        // Calculate current speed
        currentSpeed = Vector3.Distance(transform.position, lastPosition) / Time.deltaTime;
        lastPosition = transform.position;

        // Check for terrain contact and paint smoothly
        if (smoothTrail && currentSpeed >= minSpeed)
        {
            CheckTerrainContactSmooth();
        }

        // Reset terrain with R key
        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
        }
    }

    void CheckTerrainContactSmooth()
    {
        if (terrain == null) return;

        // Raycast downward to check if we're on terrain
        RaycastHit hit;
        Vector3 rayStart = transform.position + Vector3.up * 0.5f;

        if (Physics.Raycast(rayStart, Vector3.down, out hit, 2f))
        {
            // Check if we hit the terrain
            if (hit.collider.GetComponent<TerrainCollider>())
            {
                isGrounded = true;

                // Paint smooth trail between last position and current position
                PaintSmoothTrail(lastPaintPosition, hit.point);
                lastPaintPosition = hit.point;
            }
            else
            {
                isGrounded = false;
            }
        }
        else
        {
            isGrounded = false;
        }
    }

    void PaintSmoothTrail(Vector3 startPos, Vector3 endPos)
    {
        float distance = Vector3.Distance(startPos, endPos);
        int steps = Mathf.Max(1, Mathf.RoundToInt(distance / (paintRadius * 0.5f)));

        for (int i = 0; i <= steps; i++)
        {
            float t = (float)i / steps;
            Vector3 paintPos = Vector3.Lerp(startPos, endPos, t);

            // Check if this position should be painted
            if (IsTargetLayer(paintPos))
            {
                // Calculate paint strength based on speed - higher speed = stronger painting
                float speedFactor = Mathf.Clamp(currentSpeed / minSpeed, 0.5f, 3f); // Min 0.5x, Max 3x intensity
                PaintTextureAtWithIntensity(paintPos, paintIntensity * speedFactor);
            }
        }
    }

    // Alternative collision method (keep both for better compatibility)
    void OnCollisionStay(Collision collision)
    {
        if (terrain == null) return;

        // Check if colliding with Terrain
        if (collision.collider.GetComponent<TerrainCollider>())
        {
            // Loop through all contact points with terrain
            foreach (ContactPoint contact in collision.contacts)
            {
                if (IsTargetLayer(contact.point))
                {
                    PaintTextureAt(contact.point);
                }
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];

        return targetWeight >= 0.5f;
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        PaintTextureAtWithIntensity(worldPos, 1f);
    }

    void PaintTextureAtWithIntensity(Vector3 worldPos, float intensity)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        // Convert world position to alphamap coordinates
        float mapX = (terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth;
        float mapZ = (terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight;

        // Calculate brush size in alphamap units for rectangle
        float brushSizeX = (paintRadius / terrainData.size.x) * terrainData.alphamapWidth;
        float brushSizeZ = (paintRadius / terrainData.size.z) * terrainData.alphamapHeight;

        int halfBrushX = Mathf.RoundToInt(brushSizeX);
        int halfBrushZ = Mathf.RoundToInt(brushSizeZ);

        // Calculate rectangle bounds
        int startX = Mathf.Clamp(Mathf.RoundToInt(mapX) - halfBrushX, 0, terrainData.alphamapWidth - 1);
        int startZ = Mathf.Clamp(Mathf.RoundToInt(mapZ) - halfBrushZ, 0, terrainData.alphamapHeight - 1);
        int endX = Mathf.Clamp(Mathf.RoundToInt(mapX) + halfBrushX, 0, terrainData.alphamapWidth - 1);
        int endZ = Mathf.Clamp(Mathf.RoundToInt(mapZ) + halfBrushZ, 0, terrainData.alphamapHeight - 1);

        int actualWidth = endX - startX + 1;
        int actualHeight = endZ - startZ + 1;

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, actualWidth, actualHeight);

        // Paint in rectangle pattern with 100% intensity
        for (int x = 0; x < actualWidth; x++)
        {
            for (int z = 0; z < actualHeight; z++)
            {
                float currentWeight = alphaMaps[x, z, targetLayerIndex];
                if (currentWeight >= 0.5f)
                {
                    // Apply 100% intensity - complete texture replacement
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        if (i == applyLayerIndex)
                        {
                            alphaMaps[x, z, i] = 1f; // 100% of target texture
                        }
                        else
                        {
                            alphaMaps[x, z, i] = 0f; // 0% of other textures
                        }
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }


}
